(function () {
  "use strict";
  let orderApiToken = null;
  function getApiToken() {
    if (window.PXUTheme && window.PXUTheme.orderApiToken) {
      if (Array.isArray(window.PXUTheme.orderApiToken)) {
        orderApiToken =
          window.PXUTheme.orderApiToken[0]?.api_token ||
          window.PXUTheme.orderApiToken[0];
      } else if (typeof window.PXUTheme.orderApiToken === "object") {
        orderApiToken =
          window.PXUTheme.orderApiToken.api_token ||
          window.PXUTheme.orderApiToken;
      } else {
        orderApiToken = window.PXUTheme.orderApiToken;
      }
    }
    return orderApiToken;
  }

  // Configuration - Update these with your actual API details
  const API_CONFIG = {
    // Replace with your actual third-party API endpoint
    endpoint: "https://corsapi-dev.cuddleclones.com//order-tracking",
    getHeaders: function () {
      const token = getApiToken();
      return {
        "Content-Type": "application/json",
        "api-token": token,
        Authorization: `Bearer ${token}`,
      };
    },
  };

  let form,
    orderNumberInput,
    customerEmailInput,
    successMessage,
    errorMessage,
    resultsContainer,
    trackButton;

  function init() {
    form = document.getElementById("order-tracking-form");
    orderNumberInput = document.getElementById("order-number");
    customerEmailInput = document.getElementById("customer-email");
    successMessage = document.getElementById("tracking-success");
    errorMessage = document.getElementById("tracking-error");
    resultsContainer = document.getElementById("order-status-results");
    trackButton = document.getElementById("track-order-btn");

    if (!form) {
      console.warn("Order tracking form not found");
      return;
    }

    // Auto-fill form fields from URL parameters
    autoFillFromUrlParams();

    form.addEventListener("submit", handleFormSubmit);
    orderNumberInput.addEventListener("input", validateOrderNumber);
    customerEmailInput.addEventListener("input", validateEmail);
  }

  // Auto-fill form fields from URL parameters
  function autoFillFromUrlParams() {
    const urlParams = new URLSearchParams(window.location.search);
    const orderParam = urlParams.get("order");
    const emailParam = urlParams.get("email");

    if (orderParam && orderNumberInput) {
      // Remove hashtag (#) from order number if present
      const cleanOrderNumber = orderParam.replace(/^#/, "");
      orderNumberInput.value = cleanOrderNumber;
    }

    if (emailParam && customerEmailInput) {
      customerEmailInput.value = emailParam;
    }
  }

  function handleFormSubmit(event) {
    event.preventDefault();
    hideMessages();
    const orderNumber = orderNumberInput.value.trim();
    const customerEmail = customerEmailInput.value.trim();
    if (!validateForm(orderNumber, customerEmail)) {
      return;
    }
    setLoadingState(true);
    trackOrder(orderNumber, customerEmail);
  }

  function validateForm(orderNumber, customerEmail) {
    let isValid = true;
    let errorMsg = "";

    if (!orderNumber) {
      errorMsg += "Order number is required. ";
      isValid = false;
    }

    if (!customerEmail) {
      errorMsg += "Email address is required. ";
      isValid = false;
    } else if (!isValidEmail(customerEmail)) {
      errorMsg += "Please enter a valid email address. ";
      isValid = false;
    }

    if (!isValid) {
      showError(errorMsg);
    }

    return isValid;
  }

  function validateOrderNumber() {
    const orderNumber = orderNumberInput.value.trim();
    if (orderNumber && orderNumber.length < 3) {
      orderNumberInput.setCustomValidity(
        "Order number must be at least 3 characters long"
      );
    } else {
      orderNumberInput.setCustomValidity("");
    }
  }

  function validateEmail() {
    const email = customerEmailInput.value.trim();

    if (email && !isValidEmail(email)) {
      customerEmailInput.setCustomValidity(
        "Please enter a valid email address"
      );
    } else {
      customerEmailInput.setCustomValidity("");
    }
  }

  function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  // Make API call to track order
  async function trackOrder(orderNumber, customerEmail) {
    try {
      const requestData = {
        orderNumber: orderNumber,
        email: customerEmail,
      };

      const response = await fetch(API_CONFIG.endpoint, {
        method: "POST",
        headers: API_CONFIG.getHeaders(),
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      // Handle successful response
      handleTrackingSuccess(data);
    } catch (error) {
      console.error("Error tracking order:", error);
      handleTrackingError(error);
    } finally {
      setLoadingState(false);
    }
  }

  // Handle successful tracking response
  function handleTrackingSuccess(data) {
    hideMessages();
    showSuccess(
      "Order found! Here are the details click on Link to view full order status:"
    );
    displayOrderStatus(data);
  }

  // Handle tracking error
  function handleTrackingError(error) {
    let errorMsg = "Unable to track your order. ";

    if (error.message.includes("404")) {
      errorMsg =
        "Order not found. Please check your order number and email address.";
    } else if (error.message.includes("500")) {
      errorMsg = "Server error. Please try again later.";
    } else if (error.name === "TypeError" && error.message.includes("fetch")) {
      errorMsg =
        "Network error. Please check your internet connection and try again.";
    } else {
      errorMsg += "Please try again or contact customer support.";
    }

    showError(errorMsg);
  }

  // Display order status information
  function displayOrderStatus(orderData) {
    const statusContent = resultsContainer.querySelector(
      ".order-status-content"
    );

    let html = "<h3>📦 Order Details</h3>";
    html += '<div class="order-section">';
    if (orderData.orderStatusUrl) {
      html += `<div class="status-item status-url-item">
        <div class="status-url-container">
          <a href="${orderData.orderStatusUrl}" target="_blank" class="status-url-link">
            <span>🔍</span> View Complete Status
          </a>
          <button type="button" class="copy-url-btn" onclick="copyToClipboard('${orderData.orderStatusUrl}', this)">
            📋 Copy Link
          </button>
        </div>
      </div>`;
    }

    statusContent.innerHTML = html;
    resultsContainer.style.display = "block";

    // Smooth scroll to results
    resultsContainer.scrollIntoView({
      behavior: "smooth",
      block: "start",
    });
  }

  // Show success message
  function showSuccess(message) {
    successMessage.textContent = message;
    successMessage.style.display = "block";
    errorMessage.style.display = "none";
  }

  // Show error message
  function showError(message) {
    errorMessage.textContent = message;
    errorMessage.style.display = "block";
    successMessage.style.display = "none";
    resultsContainer.style.display = "none";
  }

  // Hide all messages
  function hideMessages() {
    successMessage.style.display = "none";
    errorMessage.style.display = "none";
  }

  // Set loading state
  function setLoadingState(isLoading) {
    if (isLoading) {
      trackButton.textContent = "Tracking...";
      trackButton.disabled = true;
      form.style.opacity = "0.7";
    } else {
      trackButton.textContent = "Track Order";
      trackButton.disabled = false;
      form.style.opacity = "1";
    }
  }

  // Helper function to copy URL to clipboard
  window.copyToClipboard = function (text, buttonElement) {
    navigator.clipboard
      .writeText(text)
      .then(function () {
        // Show temporary success message
        const btn = buttonElement || event.target;
        const originalText = btn.textContent;
        btn.textContent = "Copied!";
        btn.style.backgroundColor = "#28a745";
        setTimeout(() => {
          btn.textContent = originalText;
          btn.style.backgroundColor = "";
        }, 2000);
      })
      .catch(function (err) {
        console.error("Could not copy text: ", err);
        // Fallback for older browsers
        const textArea = document.createElement("textarea");
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand("copy");
        document.body.removeChild(textArea);
      });
  };

  // Initialize when DOM is ready
  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", init);
  } else {
    init();
  }
})();
