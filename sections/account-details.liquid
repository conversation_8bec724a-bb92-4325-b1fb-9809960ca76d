{% comment %}
  ** Account details template - main content area **
{% endcomment %}

{% assign id = section.id %}
{% comment %}Layout{% endcomment %}
{% assign width = section.settings.width %}
{% assign padding_top = section.settings.padding_top %}
{% assign padding_bottom = section.settings.padding_bottom %}
{% comment %}Advanced{% endcomment %}
{% assign css_class = section.settings.css_class %}
{% assign custom_css = section.settings.custom_css %}

{% style %}
  .section__wrapper {
    padding-top: {{ padding_top }}px;
    padding-bottom: {{ padding_bottom }}px;
  }

  {% render 'css-loop',
          css: section_css,
          id: id
  %}
  {% render 'css-loop',
          css: custom_css,
          id: id
  %}
{% endstyle %}

{% comment %} HTML markup {% endcomment %}
{% paginate customer.orders by 20 %}
  <section class="section section__wrapper is-width-{{ width }} {{ css_class }}">
    <header class="container">
      {%- capture title -%}{{ 'customer.account.details' | t }}{%- endcapture -%}
      {% render 'heading', title: title, heading_tag: 'h1', context: 'account-details', text_alignment: 'left' %}
    </header>

    <div class="container">
      <aside class="one-fourth medium-down--one-whole column account-sidebar">
        <div class="account-sidebar__content has-padding-top">
          <h4>
            <a href="{{ routes.account_url }}" title="{{ 'layout.customer.my_account' | t }}">{{ customer.name }}</a>
          </h4>
          <p class="account-sidebar__email">{{ customer.email }}</p>
          <h4 class="has-padding-top">{{ 'customer.account.primary_address' | t }}</h4>
          <div class="account-sidebar__address">
            {% if customer.default_address != null %}
              <p>{{ customer.default_address.address1 }}</p>
              {% if customer.default_address.address2 != '' %}
                <p>{{ customer.default_address.address2 }}</p>
              {% endif %}
              <p>
                {{ customer.default_address.city -}}
                {%- if customer.default_address.province_code -%}
                  , {{ customer.default_address.province_code -}}
                {%- endif %}
                {{ customer.default_address.zip }}
              </p>
              <p>{{ customer.default_address.country }}</p>
              <p>{{ customer.default_address.phone }}</p>
            {% else %}
              <p>{{ 'customer.addresses.no_addresses' | t }}</p>
            {% endif %}
            <p>
              <a href="{{ routes.account_addresses_url }}">
                {{- 'customer.account.view_addresses' | t }} ({{ customer.addresses_count }})</a
              >
            </p>
          </div>
        </div>
        <div class="has-padding-top">
          {% capture logout_link %}{{ routes.account_logout_url }}{% endcapture %}
          {% capture logout_label %}{{ 'layout.customer.log_out' | t }}{% endcapture %}
          {% render 'button',
            label: logout_label,
            type: 'link',
            href: logout_link,
            style: 'button--secondary',
            attribute: 'data-no-instant'
          %}
        </div>
      </aside>
      <main class="three-fourths medium-down--one-whole column account-main has-padding-top">
        <div id="customer_orders">
          <h2 class="title">{{ 'customer.orders.title' | t }}</h2>

          {% if customer.orders.size != 0 %}
            <table class="table is-bordered" width="100%">
              <thead class="text-align-left">
                <tr>
                  <th>{{ 'customer.orders.order_number' | t }}</th>
                  <th>{{ 'customer.orders.date' | t }}</th>
                  <th>{{ 'customer.orders.payment_status' | t }}</th>
                  <th>{{ 'customer.orders.fulfillment_status' | t }}</th>
                  <th>{{ 'customer.orders.total' | t }}</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {% for order in customer.orders %}
                  <tr class="{% cycle 'odd', 'even' %} {% if order.cancelled %}cancelled_order{% endif %}">
                    <td>{{ order.name | link_to: order.customer_url }}</td>
                    <td>{{ order.created_at | date: format: 'month_day_year' }}</td>
                    <td>{{ order.financial_status_label }}</td>
                    <td>{{ order.fulfillment_status_label }}</td>
                    <td>
                      <span>
                        {%- render 'price-element', price: order.total_price %}
                        {{ order.currency -}}
                      </span>
                    </td>
                    <td>
                      <button
                        class="btn track-order-btn"
                        onclick="openTrackingPage('{{ order.name }}')"
                        style="background-color: #007bff; color: white; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; font-size: 14px;"
                      >
                        Track Order
                      </button>
                    </td>
                  </tr>
                {% endfor %}
              </tbody>
            </table>
          {% else %}
            <p>{{ 'customer.orders.none' | t }}</p>
          {% endif %}

          <div class="one-whole column text-align-center">
            {% render 'pagination', paginate: paginate %}
          </div>
        </div>
      </main>
    </div>
  </section>
{% endpaginate %}

<script>
  function openTrackingPage(orderNumber) {
    // Redirect to the order tracking page
    const trackingUrl = '/pages/track-order?order=' + encodeURIComponent(orderNumber);
    window.location.href = trackingUrl;
  }
</script>

{% schema %}
{
  "name": "Account details",
  "class": "account-section",
  "settings": [
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "select",
      "id": "width",
      "label": "Width",
      "default": "standard",
      "options": [
        {
          "value": "standard",
          "label": "Standard"
        },
        {
          "value": "wide",
          "label": "Wide"
        }
      ]
    },
    {
      "type": "range",
      "id": "padding_top",
      "label": "Top spacing",
      "min": 0,
      "max": 80,
      "default": 40,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "label": "Bottom spacing",
      "min": 0,
      "max": 80,
      "default": 40,
      "unit": "px"
    },
    {
      "type": "header",
      "content": "Advanced"
    },
    {
      "type": "paragraph",
      "content": "[Learn more](https://help.outofthesandbox.com/hc/en-us/articles/************)"
    },
    {
      "type": "text",
      "id": "css_class",
      "label": "CSS Class"
    },
    {
      "type": "textarea",
      "id": "custom_css",
      "label": "Custom CSS"
    }
  ]
}
{% endschema %}
