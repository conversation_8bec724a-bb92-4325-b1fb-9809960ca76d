{% comment %} Order Status Tracking Page {% endcomment %}

{% liquid
  if settings.form_button_style contains 'primary'
    assign field_input_size = settings.button_primary_padding
    assign field_input_style = 'primary-btn-style'
  elsif settings.form_button_style contains 'secondary'
    assign field_input_size = settings.button_secondary_padding
    assign field_input_style = 'secondary-btn-style'
  else
    assign field_input_size = settings.button_link_padding
  endif
%}

<section class="section-orderstatus">
  <div class="container content contact-form contact-form--center">
    <div class="two-thirds offset-by-three medium-down--one-whole column">
      <header class="page-header">
        <h1 class="page-title">{{ page.title }}</h1>
        {% if page.content != blank %}
          <div class="page-content">
            {{ page.content }}
          </div>
        {% endif %}
      </header>
      dfghjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjj

      <div class="contact-form__form">
        <div class="form__success-message" id="tracking-success" style="display: none;"></div>
        <div class="one-whole column contact-form__form-errors">
          <p class="form__error" id="tracking-error" style="display: none;"></p>
        </div>

        <form id="order-tracking-form" class="contact-form contact-form--contact-section">
          <input type="hidden" name="challenge" value="false">

          <div class="contact-form__blocks">
            <div class="container">
              <!-- Order Number Field -->
              <div class="one-whole column">
                <div class="contact-form__block contact-form__block--textfield">
                  <label for="order-number" class="label {% if settings.use_placeholders %}is-sr-only{% endif %}">
                    Order Number <span class="required">*</span>
                  </label>
                  <input
                    type="text"
                    placeholder="{% if settings.use_placeholders %}Order Number*{% endif %}"
                    name="orderNumber"
                    id="order-number"
                    class="input is-{{ field_input_style }} is-{{ field_input_size }}"
                    required="required"
                  >
                </div>
              </div>

              <!-- Customer Email Field -->
              <div class="one-whole column">
                <div class="contact-form__block contact-form__block--email">
                  <label for="customer-email" class="label {% if settings.use_placeholders %}is-sr-only{% endif %}">
                    Customer Email <span class="required">*</span>
                  </label>
                  <input
                    type="email"
                    placeholder="{% if settings.use_placeholders %}Customer Email*{% endif %}"
                    id="customer-email"
                    class="input is-{{ field_input_style }} is-{{ field_input_size }}"
                    name="customerEmail"
                    autocorrect="off"
                    autocapitalize="off"
                    required="required"
                  >
                </div>
              </div>
            </div>
          </div>

          <div class="container">
            <div class="one-whole column">
              {% assign submit_label = 'Track Order' %}
              {% render 'button',
                label: submit_label,
                type: 'submit',
                style: settings.form_button_style,
                class: 'is-within-form',
                attribute: 'id="track-order-btn"'
              %}
            </div>
          </div>
        </form>

        <!-- Order Status Results -->
        <div id="order-status-results" class="order-status-results" style="display: none;">
          <div class="order-status-content">
            <!-- Results will be populated by JavaScript -->
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

{% style %}
    /* Order Tracking Form Card */
    .contact-form__form {
      background: {{ settings.shop_bg_color | color_lighten: 5 }};
      border-radius: 12px;
      padding: 2rem;
      margin-top: 2rem;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      border: 1px solid {{ settings.border_color | default: '#e1e1e1' }};
    }

    .contact-form__blocks .container {
      padding: 0;
    }

    .contact-form__block {
      margin-bottom: 1.5rem;
    }

    .contact-form__block .label {
      font-weight: 600;
      margin-bottom: 0.5rem;
      color: {{ settings.regular_color | default: '#333' }};
      font-size: 1rem;
    }

    .contact-form__block .input {
      border-radius: 8px;
      border: 2px solid {{ settings.border_color | default: '#e1e1e1' }};
      padding: 1rem;
      font-size: 1rem;
      transition: all 0.3s ease;
      background: white;
    }

    .contact-form__block .input:focus {
      border-color: {{ settings.accent_color | default: '#007bff' }};
      box-shadow: 0 0 0 3px {{ settings.accent_color | default: '#007bff' | color_modify: 'alpha', 0.1 }};
      outline: none;
    }

    .required {
      color: #e74c3c;
    }

    /* Form Success/Error Messages */
    .form__success-message {
      background: #d4edda;
      color: #155724;
      padding: 1rem;
      border-radius: 8px;
      margin-bottom: 1.5rem;
      border: 1px solid #c3e6cb;
      font-weight: 500;
    }

    .form__error {
      background: #f8d7da;
      color: #721c24;
      padding: 1rem;
      border-radius: 8px;
      margin-bottom: 1.5rem;
      border: 1px solid #f5c6cb;
      font-weight: 500;
    }

    /* Order Results Card */
    .order-status-results {
      background: white;
      border-radius: 12px;
      padding: 2rem;
      margin-top: 2rem;
      margin-bottom: 4rem;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      border: 1px solid {{ settings.border_color | default: '#e1e1e1' }};
    }

    .order-status-content h3 {
      color: {{ settings.heading_color | default: '#333' }};
      margin-bottom: 1.5rem;
      font-size: 1.5rem;
      font-weight: 700;
      border-bottom: 2px solid {{ settings.accent_color | default: '#007bff' }};
      padding-bottom: 0.5rem;
    }

    .order-status-content h4 {
      color: {{ settings.heading_color | default: '#333' }};
      margin: 1.5rem 0 1rem 0;
      font-size: 1.2rem;
      font-weight: 600;
    }

    .status-label {
      font-weight: 600;
      color: {{ settings.regular_color | default: '#555' }};
      min-width: 140px;
      flex-shrink: 0;
    }

    .status-value {
      text-align: right;
      flex-grow: 1;
    }

    /* Status Colors */
    .status-unfulfilled {
      color: #ffc107;
      font-weight: 600;
      background: #fff3cd;
      padding: 0.25rem 0.5rem;
      border-radius: 4px;
      font-size: 0.9rem;
    }

    .status-fulfilled {
      color: #28a745;
      font-weight: 600;
      background: #d4edda;
      padding: 0.25rem 0.5rem;
      border-radius: 4px;
      font-size: 0.9rem;
    }

    /* Order Status URL Highlight */
    .status-url-item {
      background: linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%);
      border: 2px solid {{ settings.accent_color | default: '#007bff' }};
      border-radius: 12px;
      padding: 1.5rem !important;
      margin: 1.5rem 0;
    }

    .status-url-container {
      display: flex;
      gap: 12px;
      align-items: center;
      flex-wrap: wrap;
      margin-top: 0.75rem;
    }

    .status-url-link {
      background: {{ settings.accent_color | default: '#007bff' }};
      color: white;
      padding: 12px 24px;
      text-decoration: none;
      border-radius: 8px;
      font-weight: 600;
      transition: all 0.3s ease;
      display: inline-flex;
      align-items: center;
      gap: 8px;
    }

    .status-url-link:hover {
      background: {{ settings.accent_color | default: '#007bff' | color_darken: 10 }};
      color: white;
      text-decoration: none;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
    }

    .copy-url-btn {
      background: #6c757d;
      color: white;
      border: none;
      padding: 12px 20px;
      border-radius: 8px;
      cursor: pointer;
      font-weight: 600;
      transition: all 0.3s ease;
    }

    .copy-url-btn:hover {
      background: #545b62;
      transform: translateY(-1px);
    }

    /* Order Sections */
    .order-section {
      background: #fafafa;
      border-radius: 8px;
      padding: 1.5rem;
      margin-bottom: 1.5rem;
      border: 1px solid #f0f0f0;
    }
    .offset-by-three {
      left: 0;
  }

    /* Page Bottom Spacing */
    .section-orderstatus {
      margin-bottom: 3rem;
      width: 70%;
    }


    .announcement-bar{
      width:100%
    }
    /* Responsive Design */
    @media only screen and (max-width: 798px) {
      .contact-form__form,
      .order-status-results {
        padding: 1.5rem;
        margin-top: 1.5rem;
      }

      .order-status-results {
        margin-bottom: 3rem;
      }

      .status-label {
        min-width: auto;
      }

      .status-value {
        text-align: left;
      }

      .status-url-container {
        flex-direction: column;
        align-items: stretch;
      }

      .status-url-link,
      .copy-url-btn {
        text-align: center;
        width: 100%;
        justify-content: center;
      }

      .line-item-details {
        grid-template-columns: 1fr;
      }
    }

    @media only screen and (max-width: 480px) {
      .contact-form__form,
      .order-status-results {
        padding: 1rem;
        margin-top: 1rem;
      }

      .order-status-results {
        margin-bottom: 2rem;
      }

      .section-orderstatus {
        margin-bottom: 2rem;
        width: 50%;
      }
    }
{% endstyle %}

{% assign order_api_token = shop.metafields.cuddleclones.Order_ApiToken.value %}
{% assign cors_api_url = shop.metafields.cuddleclones.corse_ApiUrl.value %}
<script>
  window.PXUTheme = window.PXUTheme || {};
  window.PXUTheme.orderApiToken = {{ order_api_token | json }};
  window.PXUTheme.corsApiUrl = {{ cors_api_url | json }};
</script>

<script src="{{ 'z__JsOrderTracking.js' | asset_url }}" defer></script>
